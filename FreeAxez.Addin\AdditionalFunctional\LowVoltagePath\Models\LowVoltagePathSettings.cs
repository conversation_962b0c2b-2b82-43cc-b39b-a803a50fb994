using Autodesk.Revit.DB;
using Autodesk.Revit.DB.Architecture;
using FreeAxez.Addin.Infrastructure.UI.ViewModels;

namespace FreeAxez.Addin.AdditionalFunctional.LowVoltagePath.Models
{
    /// <summary>
    /// Settings model for Low Voltage Path command execution
    /// </summary>
    public class LowVoltagePathSettings : ViewModelBase
    {
        private ScopeType _scopeType = ScopeType.EntireView;
        private RailingType _selectedRailingType;
        private bool _deleteLines = false;

        /// <summary>
        /// Scope of operation - entire view or selected elements only
        /// </summary>
        public ScopeType ScopeType
        {
            get => _scopeType;
            set => Set(ref _scopeType, value);
        }

        /// <summary>
        /// Target railing type for creating railings
        /// </summary>
        public RailingType SelectedRailingType
        {
            get => _selectedRailingType;
            set => Set(ref _selectedRailingType, value);
        }

        /// <summary>
        /// Whether to delete lines after converting them to railings
        /// </summary>
        public bool DeleteLines
        {
            get => _deleteLines;
            set => Set(ref _deleteLines, value);
        }

        /// <summary>
        /// Available railing types in the project
        /// </summary>
        public List<RailingType> AvailableRailingTypes { get; set; } = new List<RailingType>();
    }

    /// <summary>
    /// Scope type for the operation
    /// </summary>
    public enum ScopeType
    {
        /// <summary>
        /// Process entire view
        /// </summary>
        EntireView,

        /// <summary>
        /// Process only selected elements
        /// </summary>
        SelectedElements
    }
}
