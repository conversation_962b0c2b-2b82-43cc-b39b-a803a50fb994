namespace FreeAxez.Addin.AdditionalFunctional.LowVoltagePath.Models
{
    /// <summary>
    /// Result of validation before executing Low Voltage Path command
    /// </summary>
    public class ValidationResult
    {
        /// <summary>
        /// Whether validation passed successfully
        /// </summary>
        public bool IsValid { get; set; }

        /// <summary>
        /// List of validation error messages
        /// </summary>
        public List<string> ErrorMessages { get; set; } = new List<string>();

        /// <summary>
        /// List of validation warning messages
        /// </summary>
        public List<string> WarningMessages { get; set; } = new List<string>();

        /// <summary>
        /// Number of valid outlets found
        /// </summary>
        public int ValidOutletsCount { get; set; }

        /// <summary>
        /// Number of valid lines found
        /// </summary>
        public int ValidLinesCount { get; set; }

        /// <summary>
        /// Number of outlets that already have railings
        /// </summary>
        public int OutletsWithExistingRailingsCount { get; set; }

        /// <summary>
        /// Creates a successful validation result
        /// </summary>
        public static ValidationResult Success(int validOutlets, int validLines, int outletsWithRailings = 0)
        {
            return new ValidationResult
            {
                IsValid = true,
                ValidOutletsCount = validOutlets,
                ValidLinesCount = validLines,
                OutletsWithExistingRailingsCount = outletsWithRailings
            };
        }

        /// <summary>
        /// Creates a failed validation result with error message
        /// </summary>
        public static ValidationResult Failure(string errorMessage)
        {
            return new ValidationResult
            {
                IsValid = false,
                ErrorMessages = new List<string> { errorMessage }
            };
        }

        /// <summary>
        /// Creates a failed validation result with multiple error messages
        /// </summary>
        public static ValidationResult Failure(List<string> errorMessages)
        {
            return new ValidationResult
            {
                IsValid = false,
                ErrorMessages = errorMessages
            };
        }

        /// <summary>
        /// Adds a warning message to the validation result
        /// </summary>
        public void AddWarning(string warningMessage)
        {
            WarningMessages.Add(warningMessage);
        }

        /// <summary>
        /// Adds an error message to the validation result
        /// </summary>
        public void AddError(string errorMessage)
        {
            ErrorMessages.Add(errorMessage);
            IsValid = false;
        }

        /// <summary>
        /// Gets all messages (errors and warnings) combined
        /// </summary>
        public List<string> GetAllMessages()
        {
            var allMessages = new List<string>();
            allMessages.AddRange(ErrorMessages);
            allMessages.AddRange(WarningMessages);
            return allMessages;
        }
    }
}
