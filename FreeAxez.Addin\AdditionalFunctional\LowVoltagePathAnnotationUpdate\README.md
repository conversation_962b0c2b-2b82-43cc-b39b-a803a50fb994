# Update Low Voltage Annotation Command

## Overview

The Update Low Voltage Annotation Command is a Revit command that updates wire count parameters in existing low voltage railings and annotations based on connected outlets. This command works on the entire active view without requiring user interface interaction.

## Purpose

This command addresses the workflow of updating existing low voltage electrical pathways by:
- Finding connected outlets for each railing using geometric proximity
- Calculating total wire counts from outlet LV/MC parameters
- Updating Quantity parameters in railings based on connected outlets
- Updating Quantity parameters in annotations based on nearby railings
- Providing feedback on the number of elements processed

## Business Logic

### Input Requirements
- **Active View**: Must be a floor plan view
- **Existing Elements**: Railings with Quantity parameters and/or low voltage annotations
- **Outlets**: Electrical fixtures with LV/MC count parameters (optional)

### Processing Steps
1. **View Validation**: Ensures the active view is a floor plan
2. **Data Collection**: 
   - Collects all railings with Quantity parameters in the active view
   - Collects all low voltage pathway annotations in the active view
   - Collects all electrical fixtures with LV/MC parameters in the active view
3. **Wire Count Updates**:
   - For each railing: finds connected outlets within tolerance distance and sums their wire counts
   - For each annotation: finds nearby railings within tolerance distance and sums their wire counts
4. **User Feedback**: Shows summary of processed elements

### Geometric Logic
- **Outlet-to-Railing Connection**: Uses 2-foot tolerance distance (LowVoltageConstants.OutletToLineToleranceFeet)
- **Annotation-to-Railing Association**: Uses 1-inch tolerance distance (LowVoltageConstants.AnnotationToleranceFeet)
- **NTS Geometry**: Utilizes NetTopologySuite for precise distance calculations

### Wire Count Calculation
- **Outlet Wire Count**: Sums all LV/MC count parameters (LV1 - Count, MC 2 - Count, etc.) from each outlet
- **Railing Wire Count**: Sum of wire counts from all connected outlets
- **Annotation Wire Count**: Sum of wire counts from all nearby railings

## Architecture

### Dependencies
- **WireCountingService**: Core service for updating wire counts with outlet support
- **LowVoltageDataCollector**: Service for collecting outlets and calculating wire counts
- **NTSConverter**: Geometry conversion utilities
- **LowVoltageConstants**: Configuration constants and tolerance values

### Error Handling
- Validates view type before execution
- Graceful handling of missing elements
- Transaction rollback on errors
- User-friendly error messages

### Performance Considerations
- Processes only elements in the active view
- Uses efficient FilteredElementCollector queries
- Single transaction for all updates
- Geometric calculations optimized with NTS

## Usage

1. **Open Floor Plan**: Ensure a floor plan view is active
2. **Execute Command**: Run the Update Low Voltage Annotation command
3. **Review Results**: Check the success message for processing summary

## Integration

This command complements the main Low Voltage Path Generator by providing a way to update existing elements without recreating them. It uses the same core services and constants for consistency.

## Future Enhancements

- Support for selected elements scope
- Batch processing across multiple views
- Advanced filtering options for specific railing types
- Integration with project parameters for global settings
