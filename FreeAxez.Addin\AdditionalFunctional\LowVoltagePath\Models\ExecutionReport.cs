namespace FreeAxez.Addin.AdditionalFunctional.LowVoltagePath.Models
{
    /// <summary>
    /// Report of Low Voltage Path command execution results
    /// </summary>
    public class ExecutionReport
    {
        /// <summary>
        /// Number of railings created
        /// </summary>
        public int RailingsCreated { get; set; }

        /// <summary>
        /// Number of annotations (tags) created
        /// </summary>
        public int AnnotationsCreated { get; set; }

        /// <summary>
        /// Number of lines deleted (if delete option was enabled)
        /// </summary>
        public int LinesDeleted { get; set; }

        /// <summary>
        /// Number of outlets that could not be connected to railings
        /// </summary>
        public int UnconnectedOutlets { get; set; }

        /// <summary>
        /// List of outlet IDs that could not be connected
        /// </summary>
        public List<int> UnconnectedOutletIds { get; set; } = new List<int>();

        /// <summary>
        /// Total number of outlets processed
        /// </summary>
        public int TotalOutletsProcessed { get; set; }

        /// <summary>
        /// Total number of lines processed
        /// </summary>
        public int TotalLinesProcessed { get; set; }

        /// <summary>
        /// Execution time in milliseconds
        /// </summary>
        public long ExecutionTimeMs { get; set; }

        /// <summary>
        /// Whether the execution was successful
        /// </summary>
        public bool IsSuccessful => RailingsCreated > 0 || AnnotationsCreated > 0;

        /// <summary>
        /// Gets a formatted summary of the execution results
        /// </summary>
        public string GetSummary()
        {
            var summary = new List<string>();

            if (RailingsCreated > 0)
            {
                summary.Add($"Created {RailingsCreated} railing(s)");
            }

            if (AnnotationsCreated > 0)
            {
                summary.Add($"Created {AnnotationsCreated} annotation(s)");
            }

            if (LinesDeleted > 0)
            {
                summary.Add($"Deleted {LinesDeleted} line(s)");
            }

            if (UnconnectedOutlets > 0)
            {
                summary.Add($"Warning: {UnconnectedOutlets} outlet(s) could not be connected");
            }

            if (summary.Count == 0)
            {
                return "No elements were created or modified.";
            }

            return string.Join("\n", summary);
        }

        /// <summary>
        /// Gets a detailed report including unconnected outlet IDs
        /// </summary>
        public string GetDetailedReport()
        {
            var report = GetSummary();

            if (UnconnectedOutletIds.Count > 0)
            {
                report += $"\n\nUnconnected outlet IDs: {string.Join(", ", UnconnectedOutletIds)}";
            }

            if (ExecutionTimeMs > 0)
            {
                report += $"\n\nExecution time: {ExecutionTimeMs} ms";
            }

            return report;
        }
    }
}
