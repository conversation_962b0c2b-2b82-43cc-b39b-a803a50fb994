﻿using Autodesk.Revit.DB;
using Autodesk.Revit.DB.Architecture;
using NetTopologySuite.Geometries;
using FreeAxez.Addin.AdditionalFunctional.LowVoltagePath.Constants;
using NPOI.SS.Formula.Functions;
using FreeAxez.Addin.Infrastructure;

namespace FreeAxez.Addin.AdditionalFunctional.LowVoltagePath.Services
{
    public class WireCountingService
    {
        private readonly List<Railing> _railings;
        private readonly List<FamilyInstance> _annotations;
        private readonly List<FamilyInstance> _outlets;
        private readonly LowVoltageDataCollector _dataCollector;

        public WireCountingService(List<Railing> railings, List<FamilyInstance> annotations, List<FamilyInstance> outlets = null)
        {
            _railings = railings;
            _annotations = annotations;
            _outlets = outlets ?? new List<FamilyInstance>();
            _dataCollector = new LowVoltageDataCollector();
        }

        public void CountWires()
        {
            using (var t = new Transaction(_railings.First().Document, "Count Wires"))
            {
                t.Start();

                // First, update railing Quantity parameters based on connected outlets
                UpdateRailingWireCounts();

                // Then, update annotation Quantity parameters based on railing values
                UpdateAnnotationWireCounts();

                t.Commit();
            }
        }

        /// <summary>
        /// Updates wire count parameters in railings based on connected outlets
        /// </summary>
        private void UpdateRailingWireCounts()
        {
            foreach (var railing in _railings)
            {
                var connectedOutlet = FindConnectedOutlet(railing);
                if (connectedOutlet == null) continue;
                var totalWireCount = _dataCollector.CalculateOutletWireCount(connectedOutlet);

                // Set the Quantity parameter in the railing
                railing.LookupParameter(LowVoltageConstants.QuantityParameterName)?.Set(totalWireCount);
            }
        }

        /// <summary>
        /// Updates wire count parameters in annotations based on nearby railings
        /// </summary>
        private void UpdateAnnotationWireCounts()
        {
            foreach (var annotation in _annotations)
            {
                var location = NTSConverter.XYZToPoint((annotation.Location as LocationPoint).Point);
                var count = 0.0;

                foreach (var railing in _railings)
                {
                    var path = NTSConverter.CurveLoopToLineString(railing.GetPath().ToList());

                    if (path.Distance(location) <= LowVoltageConstants.AnnotationToleranceFeet)
                    {
                        var wireCountParam = railing.LookupParameter(LowVoltageConstants.QuantityParameterName)?.AsDouble();
                        count += wireCountParam ?? 0.0;
                    }
                }

                annotation.LookupParameter(LowVoltageConstants.QuantityParameterName)?.Set((int)count);
            }
        }

        /// <summary>
        /// Finds outlets connected to a specific railing using NTS geometry
        /// </summary>
        private FamilyInstance FindConnectedOutlet(Railing railing)
        {
            try
            {
                var railingPath = NTSConverter.CurveLoopToLineString(railing.GetPath().ToList());
                var railingStart = railingPath.StartPoint;
                var railingEnd = railingPath.EndPoint;

                foreach (var outlet in _outlets)
                {
                    var outletLocation = (outlet.Location as LocationPoint)?.Point;
                    if (outletLocation == null) continue;

                    var outletPoint = NTSConverter.XYZToPoint(outletLocation);

                    // Check if outlet is within tolerance distance of the railing path
                    if (railingStart.Distance(outletPoint) <= LowVoltageConstants.OutletToLineToleranceFeet 
                        || railingEnd.Distance(outletPoint) <= LowVoltageConstants.OutletToLineToleranceFeet)
                    {
                        return outlet;
                    }
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error($"Error finding connected outlet for railing {railing.Id}: {ex.Message}");
            }

            return null;
        }
    }
}
