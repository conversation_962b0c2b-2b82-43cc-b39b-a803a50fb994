using Autodesk.Revit.DB;
using Autodesk.Revit.DB.Architecture;
using Autodesk.Revit.UI.Selection;
using FreeAxez.Addin.AdditionalFunctional.LowVoltagePath.Models;
using FreeAxez.Addin.AdditionalFunctional.LowVoltagePath.Services;
using FreeAxez.Addin.AdditionalFunctional.LowVoltagePath.Utils;
using FreeAxez.Addin.Infrastructure;
using FreeAxez.Addin.Infrastructure.UI.Enums;
using FreeAxez.Addin.Infrastructure.UI.Services;
using FreeAxez.Addin.Infrastructure.UI.ViewModels;
using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Input;

namespace FreeAxez.Addin.AdditionalFunctional.LowVoltagePath.ViewModels
{
    /// <summary>
    /// ViewModel for Low Voltage Path dialog
    /// </summary>
    public class LowVoltagePathViewModel : WindowViewModel
    {
        private LowVoltagePathSettings _settings;

        public LowVoltagePathViewModel()
        {
            _settings = new LowVoltagePathSettings();

            // Load settings from Properties
            LoadSettings();

            // Initialize commands
            ExecuteCommand = new RelayCommand(ExecuteAsync, CanExecute);
            CancelCommand = new RelayCommand(Cancel);

            // Load available railing types
            LoadAvailableRailingTypes();

            // Set default railing type if none selected
            if (Settings.SelectedRailingType == null && AvailableRailingTypes.Count > 0)
            {
                Settings.SelectedRailingType = AvailableRailingTypes.First();
            }
        }

        #region Properties

        /// <summary>
        /// Settings for the Low Voltage Path operation
        /// </summary>
        public LowVoltagePathSettings Settings
        {
            get => _settings;
            set => Set(ref _settings, value);
        }

        /// <summary>
        /// Available railing types in the project
        /// </summary>
        public ObservableCollection<RailingType> AvailableRailingTypes { get; private set; } = new ObservableCollection<RailingType>();

        /// <summary>
        /// Available scope types for the operation
        /// </summary>
        public Array ScopeTypes => Enum.GetValues(typeof(ScopeType));

        #endregion

        #region Commands

        /// <summary>
        /// Command to execute the Low Voltage Path operation
        /// </summary>
        public ICommand ExecuteCommand { get; }

        /// <summary>
        /// Command to cancel the dialog
        /// </summary>
        public ICommand CancelCommand { get; }

        #endregion

        #region Command Handlers

        private void ExecuteAsync(object parameter)
        {
            try
            {
                // Validate settings
                if (!ValidateSettings())
                {
                    return;
                }

                // Save settings
                SaveSettings();

                // Close dialog with success result
                if (parameter is Window window)
                {
                    window.DialogResult = true;
                    window.Close();
                }
            }
            catch (Exception ex)
            {
                MessageWindow.ShowDialog($"Error during execution: {ex.Message}", MessageType.Notify);
            }
        }

        private bool CanExecute(object parameter)
        {
            return Settings?.SelectedRailingType != null;
        }

        private void Cancel(object parameter)
        {
            if (parameter is Window window)
            {
                window.DialogResult = false;
                window.Close();
            }
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// Loads available railing types from the project
        /// </summary>
        private void LoadAvailableRailingTypes()
        {
            try
            {
                var railingTypes = new FilteredElementCollector(RevitManager.Document)
                    .OfClass(typeof(RailingType))
                    .Cast<RailingType>()
                    .OrderBy(rt => rt.Name)
                    .ToList();

                AvailableRailingTypes.Clear();
                foreach (var railingType in railingTypes)
                {
                    AvailableRailingTypes.Add(railingType);
                }

                // Try to restore previously selected railing type
                var savedRailingTypeName = Properties.Settings.Default.LowVoltageSelectedRailingType;
                if (!string.IsNullOrEmpty(savedRailingTypeName))
                {
                    var savedRailingType = railingTypes.FirstOrDefault(rt => rt.Name == savedRailingTypeName);
                    if (savedRailingType != null)
                    {
                        Settings.SelectedRailingType = savedRailingType;
                    }
                }
            }
            catch (Exception ex)
            {
                MessageWindow.ShowDialog($"Failed to load railing types: {ex.Message}", MessageType.Notify);
            }
        }

        /// <summary>
        /// Validates the current settings
        /// </summary>
        private bool ValidateSettings()
        {
            var errors = new List<string>();

            if (Settings.SelectedRailingType == null)
            {
                errors.Add("Please select a railing type.");
            }

            if (AvailableRailingTypes.Count == 0)
            {
                errors.Add("No railing types found in the project.");
            }

            if (errors.Count > 0)
            {
                MessageWindow.ShowDialog(string.Join("\n", errors), MessageType.Notify);
                return false;
            }

            return true;
        }

        /// <summary>
        /// Loads settings from Properties.Settings
        /// </summary>
        private void LoadSettings()
        {
            Settings.ScopeType = (ScopeType)Properties.Settings.Default.LowVoltageScopeType;
            Settings.DeleteLines = Properties.Settings.Default.LowVoltageDeleteLines;
        }

        /// <summary>
        /// Saves settings to Properties.Settings
        /// </summary>
        private void SaveSettings()
        {
            Properties.Settings.Default.LowVoltageScopeType = (int)Settings.ScopeType;
            Properties.Settings.Default.LowVoltageDeleteLines = Settings.DeleteLines;
            Properties.Settings.Default.LowVoltageSelectedRailingType = Settings.SelectedRailingType?.Name ?? string.Empty;
            Properties.Settings.Default.Save();
        }

        /// <summary>
        /// Picks elements using selection filter after dialog closes
        /// </summary>
        public (List<CurveElement> lines, List<FamilyInstance> outlets) PickSelectedElements()
        {
            var lines = new List<CurveElement>();
            var outlets = new List<FamilyInstance>();

            try
            {
                var selectionFilter = new LowVoltageSelectionFilter();
                var selectedReferences = RevitManager.UIDocument.Selection
                    .PickObjects(ObjectType.Element, selectionFilter, "Select low voltage lines and electrical fixtures.");

                foreach (var reference in selectedReferences)
                {
                    var element = RevitManager.Document.GetElement(reference);

                    if (element is CurveElement curveElement)
                    {
                        lines.Add(curveElement);
                    }
                    else if (element is FamilyInstance familyInstance &&
                             familyInstance.Category?.Id.GetIntegerValue() == (int)BuiltInCategory.OST_ElectricalFixtures)
                    {
                        outlets.Add(familyInstance);
                    }
                }
            }
            catch (Autodesk.Revit.Exceptions.OperationCanceledException)
            {
                // User cancelled selection - this is normal
            }

            return (lines, outlets);
        }

        #endregion
    }
}
