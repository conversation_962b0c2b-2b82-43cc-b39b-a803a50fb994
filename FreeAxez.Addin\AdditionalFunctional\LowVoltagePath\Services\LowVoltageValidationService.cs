using Autodesk.Revit.DB;
using Autodesk.Revit.DB.Architecture;
using FreeAxez.Addin.AdditionalFunctional.LowVoltagePath.Constants;
using FreeAxez.Addin.AdditionalFunctional.LowVoltagePath.Models;
using FreeAxez.Addin.Infrastructure;

namespace FreeAxez.Addin.AdditionalFunctional.LowVoltagePath.Services
{
    /// <summary>
    /// Service for validating low voltage path generation prerequisites
    /// </summary>
    public class LowVoltageValidationService
    {

        /// <summary>
        /// Validates if the current view is a floor plan
        /// </summary>
        public ValidationResult ValidateViewType()
        {
            if (RevitManager.Document.ActiveView.ViewType != ViewType.FloorPlan)
            {
                return ValidationResult.Failure("This command can only be executed on a floor plan view.");
            }

            return ValidationResult.Success(0, 0);
        }

        /// <summary>
        /// Validates outlets and lines for the operation
        /// </summary>
        public ValidationResult ValidateData(List<CurveElement> lines, List<FamilyInstance> outlets, LowVoltagePathSettings settings)
        {
            var result = new ValidationResult { IsValid = true };

            // Validate lines
            if (lines.Count == 0)
            {
                if (settings?.ScopeType == ScopeType.SelectedElements)
                {
                    result.AddError("No LV or MC lines found in the selected elements.");
                }
                else
                {
                    result.AddError("No LV or MC lines found in the active view.");
                }
            }
            else
            {
                result.ValidLinesCount = lines.Count;
            }

            // Validate outlets
            if (outlets.Count == 0)
            {
                if (settings?.ScopeType == ScopeType.SelectedElements)
                {
                    result.AddError("No electrical fixtures with low voltage parameters found in the selected elements.");
                }
                else
                {
                    result.AddError("No electrical fixtures with low voltage parameters found in the current view.");
                }
            }
            else
            {
                result.ValidOutletsCount = outlets.Count;

                // Check for outlets that already have railings
                var outletsWithRailings = CountOutletsWithExistingRailings(outlets);
                result.OutletsWithExistingRailingsCount = outletsWithRailings;

                if (outletsWithRailings > 0)
                {
                    result.AddWarning($"{outletsWithRailings} outlet(s) already have connected railings and will be skipped.");
                }

                // Check if all outlets would be skipped
                if (outletsWithRailings >= outlets.Count)
                {
                    result.AddError("All outlets already have connected railings. No new railings will be created.");
                }
            }

            // Validate settings
            if (settings?.SelectedRailingType == null)
            {
                result.AddError("No railing type selected.");
            }

            return result;
        }

        /// <summary>
        /// Validates railing types availability
        /// </summary>
        public ValidationResult ValidateRailingTypes()
        {
            var railingTypes = new FilteredElementCollector(RevitManager.Document)
                .OfClass(typeof(RailingType))
                .Cast<RailingType>()
                .ToList();

            if (railingTypes.Count == 0)
            {
                return ValidationResult.Failure("No railing types found in the project. Please load railing families first.");
            }

            return ValidationResult.Success(0, 0);
        }

        /// <summary>
        /// Validates annotation families availability
        /// </summary>
        public ValidationResult ValidateAnnotationFamilies()
        {
            var annotationSymbols = new FilteredElementCollector(RevitManager.Document)
                .OfClass(typeof(FamilySymbol))
                .OfCategory(BuiltInCategory.OST_GenericAnnotation)
                .Cast<FamilySymbol>()
                .Where(s => s.FamilyName == Constants.LowVoltageConstants.AnnotationFamilyName)
                .ToList();

            if (annotationSymbols.Count == 0)
            {
                var result = ValidationResult.Success(0, 0);
                result.AddWarning($"Annotation family '{Constants.LowVoltageConstants.AnnotationFamilyName}' not found. Annotations will not be created.");
                return result;
            }

            return ValidationResult.Success(0, 0);
        }

        /// <summary>
        /// Performs comprehensive validation before execution
        /// </summary>
        public ValidationResult ValidateAll(List<CurveElement> lines, List<FamilyInstance> outlets, LowVoltagePathSettings settings)
        {
            var results = new List<ValidationResult>
            {
                ValidateViewType(),
                ValidateData(lines, outlets, settings),
                ValidateRailingTypes(),
                ValidateAnnotationFamilies()
            };

            // Combine all validation results
            var combinedResult = new ValidationResult { IsValid = true };

            foreach (var result in results)
            {
                if (!result.IsValid)
                {
                    combinedResult.IsValid = false;
                }

                combinedResult.ErrorMessages.AddRange(result.ErrorMessages);
                combinedResult.WarningMessages.AddRange(result.WarningMessages);

                // Use the data counts from the main data validation
                if (result.ValidOutletsCount > 0 || result.ValidLinesCount > 0)
                {
                    combinedResult.ValidOutletsCount = result.ValidOutletsCount;
                    combinedResult.ValidLinesCount = result.ValidLinesCount;
                    combinedResult.OutletsWithExistingRailingsCount = result.OutletsWithExistingRailingsCount;
                }
            }

            return combinedResult;
        }

        public List<FamilyInstance> GetValidOutlets(List<FamilyInstance> outlets)
        {
            var existingRailings = new FilteredElementCollector(RevitManager.Document, RevitManager.Document.ActiveView.Id)
                .OfCategory(BuiltInCategory.OST_StairsRailing)
                .WhereElementIsNotElementType()
                .Cast<Railing>()
                .ToList();

            if (existingRailings.Count == 0)
            {
                return outlets;
            }

            return outlets.Where(outlet => !IsOutletConnected(outlet, existingRailings)).ToList();
        }

        private int CountOutletsWithExistingRailings(List<FamilyInstance> outlets)
        {
            var existingRailings = new FilteredElementCollector(RevitManager.Document, RevitManager.Document.ActiveView.Id)
                .OfCategory(BuiltInCategory.OST_StairsRailing)
                .WhereElementIsNotElementType()
                .Cast<Railing>()
                .ToList();

            if (existingRailings.Count == 0)
            {
                return 0;
            }

            return outlets.Count(outlet => IsOutletConnected(outlet, existingRailings));
        }

        private bool IsOutletConnected(FamilyInstance outlet, List<Railing> railings)
        {
            var outletLocation = (outlet.Location as LocationPoint)?.Point;
            if (outletLocation == null) return false;

            var outletPoint2D = NTSConverter.XYZToPoint(outletLocation);

            foreach (var railing in railings)
            {
                try
                {
                    var railingPath = railing.GetPath();
                    foreach (Curve curve in railingPath)
                    {
                        var startPoint2D = NTSConverter.XYZToPoint(curve.GetEndPoint(0));
                        var endPoint2D = NTSConverter.XYZToPoint(curve.GetEndPoint(1));

                        if (outletPoint2D.Distance(startPoint2D) <= LowVoltageConstants.OutletToLineToleranceFeet ||
                            outletPoint2D.Distance(endPoint2D) <= LowVoltageConstants.OutletToLineToleranceFeet)
                        {
                            return true;
                        }
                    }
                }
                catch
                {
                    // Ignore railings that cause errors
                    continue;
                }
            }

            return false;
        }
    }
}
