<Window x:Class="FreeAxez.Addin.AdditionalFunctional.LowVoltagePath.Views.LowVoltagePathView"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:converters="clr-namespace:FreeAxez.Addin.AdditionalFunctional.LowVoltagePath.Views.Converters"
        mc:Ignorable="d"
        Title="Low Voltage Path Generator"
        SizeToContent="Height"
        Width="450"
        ResizeMode="NoResize"
        WindowStartupLocation="CenterOwner"
        ShowInTaskbar="False">

    <Window.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="/FreeAxez.Addin;component/Infrastructure/UI/Styles/AppStyles.xaml" />
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Window.Resources>

    <Window.Style>
        <Style TargetType="{x:Type Window}" BasedOn="{StaticResource ThemeWindow}" />
    </Window.Style>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Main Settings Panel -->
        <StackPanel Grid.Row="0">

            <!-- Scope Selection -->
            <TextBlock Text="Scope of Operation:" Style="{StaticResource TextH5}" Margin="0,0,0,10"/>
            <StackPanel Margin="20,0,0,15">
                <RadioButton Content="Entire View"
                             IsChecked="{Binding Settings.ScopeType, Converter={converters:ScopeTypeConverter}, ConverterParameter=EntireView}"
                             Style="{StaticResource RadioButtonStyle}"
                             Margin="0,5"/>
                <RadioButton Content="Selected Elements Only"
                             IsChecked="{Binding Settings.ScopeType, Converter={converters:ScopeTypeConverter}, ConverterParameter=SelectedElements}"
                             Style="{StaticResource RadioButtonStyle}"
                             Margin="0,5"/>
            </StackPanel>

            <!-- Railing Type Selection -->
            <TextBlock Text="Target Railing Type:" Style="{StaticResource TextH5}" Margin="0,0,0,10"/>
            <ComboBox ItemsSource="{Binding AvailableRailingTypes}"
                      SelectedItem="{Binding Settings.SelectedRailingType}"
                      DisplayMemberPath="Name"
                      Style="{StaticResource Combobox}"
                      Margin="20,0,0,15"
                      Height="25"/>

            <!-- Options -->
            <TextBlock Text="Options:" Style="{StaticResource TextH5}" Margin="0,0,0,10"/>
            <StackPanel Margin="20,0,0,0">
                <CheckBox Content="Delete lines after converting to railings"
                          IsChecked="{Binding Settings.DeleteLines}"
                          Style="{StaticResource CheckBoxStyle}"
                          Margin="0,5"/>
            </StackPanel>

        </StackPanel>

        <!-- Buttons -->
        <Grid Grid.Row="1" Margin="0,20,0,1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <Button Grid.Column="1"
                    Content="Execute"
                    Command="{Binding ExecuteCommand}"
                    CommandParameter="{Binding RelativeSource={RelativeSource AncestorType=Window}}"
                    Style="{StaticResource ButtonOutlinedBlue}"
                    Width="100"
                    Height="30"
                    Margin="0,0,10,0"
                    IsDefault="True"/>

            <Button Grid.Column="2"
                    Content="Cancel"
                    Command="{Binding CancelCommand}"
                    CommandParameter="{Binding RelativeSource={RelativeSource AncestorType=Window}}"
                    Style="{StaticResource ButtonOutlinedRed}"
                    Width="100"
                    Height="30"
                    IsCancel="True"/>
        </Grid>
    </Grid>
</Window>
