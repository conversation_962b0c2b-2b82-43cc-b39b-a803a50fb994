using NetTopologySuite;
using NetTopologySuite.Geometries;
using FreeAxez.Addin.AdditionalFunctional.LowVoltagePath.Models;

namespace FreeAxez.Addin.AdditionalFunctional.LowVoltagePath.Services;

public class AnnotationPlacerService
{
    private const double ANNOTATION_OFFSET = 1.58333; // 1' 7"
    private const double ANNOTATION_SIZE = 3.0;
    private const double PROXIMITY_TOLERANCE = 0.1;
    private const double SHORT_SEGMENT_THRESHOLD = 3.0;

    public List<AnnotationModel> PlaceAnnotations(List<RailingModel> railings, List<LineString> normalizedLines, Point root)
    {
        var annotations = new List<AnnotationModel>();
        if (railings == null || !railings.Any() || normalizedLines == null || !normalizedLines.Any() || root == null)
            return annotations;

        var allEndpoints = normalizedLines.SelectMany(l => new[] { l.StartPoint, l.EndPoint }).ToList();
        var endpointGroups = allEndpoints.GroupBy(p => p, new PointEqualityComparer(PROXIMITY_TOLERANCE));
        var junctionPoints = endpointGroups.Where(g => g.Count() != 2).Select(g => g.Key).ToList();

        var processedSegments = new HashSet<string>();
        int idCounter = 1;

        foreach (var junction in junctionPoints)
        {
            var connectedLines = normalizedLines
                .Where(l => l.StartPoint.IsWithinDistance(junction, PROXIMITY_TOLERANCE) || l.EndPoint.IsWithinDistance(junction, PROXIMITY_TOLERANCE))
                .ToList();

            foreach (var line in connectedLines)
            {
                var segmentKey = GetSegmentKey(line);
                if (processedSegments.Contains(segmentKey)) continue;

                var startPt = line.StartPoint;
                var endPt = line.EndPoint;

                var junctionOnSegment = junction.IsWithinDistance(startPt, PROXIMITY_TOLERANCE) ? startPt : endPt;
                var otherEndOfSegment = junctionOnSegment == startPt ? endPt : startPt;

                bool isOutgoing = junction.IsWithinDistance(root, PROXIMITY_TOLERANCE) || root.Distance(otherEndOfSegment) > root.Distance(junctionOnSegment);

                if (isOutgoing)
                {
                    var coords = line.Coordinates;
                    LineSegment firstSegment;
                    if (junctionOnSegment.Coordinate.Equals2D(coords[0]))
                    {
                        firstSegment = new LineSegment(coords[0], coords[1]);
                    }
                    else
                    {
                        firstSegment = new LineSegment(coords[coords.Length - 1], coords[coords.Length - 2]);
                    }

                    Point annotationLocation;
                    if (firstSegment.Length < SHORT_SEGMENT_THRESHOLD)
                    {
                        annotationLocation = firstSegment.ToGeometry(new GeometryFactory()).Centroid;
                    }
                    else
                    {
                        annotationLocation = GetPointAtOffset(line, junctionOnSegment, ANNOTATION_OFFSET);
                    }

                    if (annotationLocation == null) continue;

                    var orientation = DetermineOrientation(annotationLocation, normalizedLines, annotations);

                    annotations.Add(new AnnotationModel
                    {
                        Id = idCounter++,
                        Location = annotationLocation,
                        Orientation = orientation,
                        WireCount = 0
                    });

                    processedSegments.Add(segmentKey);
                }
            }
        }

        return annotations;
    }

    private Point GetPointAtOffset(LineString line, Point fromPoint, double offset)
    {
        var coordinates = line.Coordinates.ToArray();
        if (fromPoint.Distance(line.StartPoint) > fromPoint.Distance(line.EndPoint))
        {
            Array.Reverse(coordinates);
        }

        double cumulativeLength = 0;
        for (int i = 0; i < coordinates.Length - 1; i++)
        {
            var start = coordinates[i];
            var end = coordinates[i + 1];
            double segmentLength = start.Distance(end);

            if (cumulativeLength + segmentLength >= offset)
            {
                double remainingOffset = offset - cumulativeLength;
                double ratio = segmentLength == 0 ? 0 : remainingOffset / segmentLength;
                double newX = start.X + (end.X - start.X) * ratio;
                double newY = start.Y + (end.Y - start.Y) * ratio;
                return new Point(newX, newY);
            }
            cumulativeLength += segmentLength;
        }
        return null;
    }

    private Direction DetermineOrientation(Point location, List<LineString> allLines, List<AnnotationModel> existingAnnotations)
    {
        var geometryFactory = NtsGeometryServices.Instance.CreateGeometryFactory();

        var containingLine = allLines.FirstOrDefault(l => location.IsWithinDistance(l, 1e-9));
        if (containingLine == null) return Direction.Up;

        // Find the specific segment of the line the location is on
        var coords = containingLine.Coordinates;
        LineSegment closestSegment = null;
        double minDistance = double.MaxValue;

        for (int i = 0; i < coords.Length - 1; i++)
        {
            var segment = new LineSegment(coords[i], coords[i + 1]);
            double distance = segment.Distance(location.Coordinate);
            if (distance < minDistance)
            {
                minDistance = distance;
                closestSegment = segment;
            }
        }

        if (closestSegment == null) return Direction.Up; // Fallback

        var p1 = closestSegment.P0;
        var p2 = closestSegment.P1;
        var deltaX = Math.Abs(p1.X - p2.X);
        var deltaY = Math.Abs(p1.Y - p2.Y);

        var possibleDirections = deltaX > deltaY
            ? new[] { Direction.Up, Direction.Down }      // Mostly horizontal segment
            : new[] { Direction.Left, Direction.Right };  // Mostly vertical segment

        var candidateDirections = new List<Direction>();

        foreach (var direction in possibleDirections)
        {
            var orientationLine = GetOrientationLine(location, direction, geometryFactory);
            bool trajectoryConflict = false;
            foreach (var trajectoryLine in allLines)
            {
                if (orientationLine.Intersects(trajectoryLine))
                {
                    var intersection = orientationLine.Intersection(trajectoryLine);
                    if (intersection is Point p && p.IsWithinDistance(location, 1e-9))
                    {
                        continue;
                    }
                    trajectoryConflict = true;
                    break;
                }
            }
            if (!trajectoryConflict)
            {
                candidateDirections.Add(direction);
            }
        }

        if (!candidateDirections.Any())
        {
            candidateDirections.AddRange(possibleDirections);
        }

        var bestDirection = Direction.None;
        int minAnnotationConflicts = int.MaxValue;

        foreach (var direction in candidateDirections)
        {
            var orientationLine = GetOrientationLine(location, direction, geometryFactory);
            int currentConflicts = 0;
            foreach (var existing in existingAnnotations)
            {
                var existingLine = GetOrientationLine(existing.Location, existing.Orientation, geometryFactory);
                if (orientationLine.Envelope.Intersects(existingLine.Envelope))
                {
                    currentConflicts++;
                }
            }

            if (currentConflicts < minAnnotationConflicts)
            {
                minAnnotationConflicts = currentConflicts;
                bestDirection = direction;
            }

            if (minAnnotationConflicts == 0) return bestDirection;
        }

        return bestDirection != Direction.None ? bestDirection : possibleDirections.First();
    }

    private LineString GetOrientationLine(Point location, Direction direction, GeometryFactory factory)
    {
        Coordinate endCoordinate;
        switch (direction)
        {
            case Direction.Up: endCoordinate = new Coordinate(location.X, location.Y + ANNOTATION_SIZE); break;
            case Direction.Down: endCoordinate = new Coordinate(location.X, location.Y - ANNOTATION_SIZE); break;
            case Direction.Left: endCoordinate = new Coordinate(location.X - ANNOTATION_SIZE, location.Y); break;
            case Direction.Right: endCoordinate = new Coordinate(location.X + ANNOTATION_SIZE, location.Y); break;
            default: return factory.CreateLineString();
        }
        return factory.CreateLineString(new[] { location.Coordinate, endCoordinate });
    }

    private string GetSegmentKey(LineString line)
    {
        var p1 = line.GetCoordinateN(0);
        var p2 = line.GetCoordinateN(line.NumPoints - 1);
        if (p1.CompareTo(p2) > 0) (p1, p2) = (p2, p1);
        return $"{p1.X:F5},{p1.Y:F5}-{p2.X:F5},{p2.Y:F5}";
    }
}

public static class PointExtensions
{
    public static bool Equals(this Point p1, Point p2, double tolerance)
    {
        return p1.IsWithinDistance(p2, tolerance);
    }
}

public class PointEqualityComparer : IEqualityComparer<Point>
{
    private readonly double _tolerance;
    public PointEqualityComparer(double tolerance) { _tolerance = tolerance; }
    public bool Equals(Point x, Point y)
    {
        if (x == null || y == null) return false;
        return x.IsWithinDistance(y, _tolerance);
    }
    public int GetHashCode(Point obj) => ($"{Math.Round(obj.X / _tolerance)}-{Math.Round(obj.Y / _tolerance)}").GetHashCode();
}
