# Sheet Title Editor - README

## Description
This feature allows users to bulk-edit the titles of all sheets in the current Revit project. It provides a dialog window that lists all sheets and their current titles, allowing for quick modifications and updates.

## Limitations and Considerations

### Revit Specifics
- Requires an active project document to be open.

## UI

### Interface
The user interface consists of a main dialog window (`SheetTitleEditorView`) which presents a list or grid of all sheets in the project. Each entry displays the Sheet Number, Sheet Name, and an editable field for the Sheet Title. The user can modify the titles and then apply the changes.

### Interface Logic
The command collects all `ViewSheet` elements and passes them to the `SheetTitleEditorViewModel`. The view binds to this data. When the user confirms the changes, the command iterates through the models and updates the corresponding `ViewSheet` elements within a single Revit transaction.

## Input Data Validation

### Pre-execution Checks
1.  **"No active document"** → The underlying framework ensures a document is open.
2.  **No Changes Made** → If the user closes the dialog or clicks "OK" without altering any titles, the operation is cancelled, and a notification is shown.

### Data Model Validation
- The feature collects all elements of type `ViewSheet`. No other element types are considered.
- The `SheetTitleModel` wraps the `ViewSheet` and tracks changes to its title.

## Report
After successfully updating the sheet titles, a report is displayed in a dialog window. The report lists all the sheets that were modified, showing their Sheet Number, Sheet Name, and the new `SheetTitle`.

```
The following sheet titles were updated:
[Sheet Number] - [Sheet Name]
`[New Sheet Title]`

...
```

## Architecture

### Main Components
```
SheetTitleEditor/
├── SheetTitleEditorCommand.cs  # Main command entry point
├── Models/
│   └── SheetTitleModel.cs      # Data model for a sheet
├── ViewModels/
│   └── SheetTitleEditorViewModel.cs # Logic for the view
└── Views/
    └── SheetTitleEditorView.xaml   # The main UI window
```

## Key Classes

### SheetTitleEditorCommand.cs
- **Purpose**: The entry point for the feature. It launches the UI and processes the results.
- **Inherits**: `BaseExternalCommand` (which likely implements `IExternalCommand`)
- **Key Methods**: `Execute()`
- **Notes**: Gathers all sheets, shows the editor window, and commits the changes back to the Revit model in a single transaction.

### SheetTitleModel.cs
- **Purpose**: Represents a single sheet and its properties.
- **Key Properties**: `SheetNumber`, `SheetName`, `SheetTitle`, `IsTitleChanged`.
- **Key Methods**: `UpdateSheetTitleInModel()` which updates the underlying `ViewSheet` element.

### SheetTitleEditorViewModel.cs
- **Purpose**: Manages the state and logic of the `SheetTitleEditorView`.
- **Dependencies**: `ObservableCollection<SheetTitleModel>`.
- **Notes**: Handles the presentation of sheet data to the UI.

### SheetTitleEditorView.xaml
- **Purpose**: The main user interface for editing sheet titles.
- **Notes**: A WPF window that allows users to see and edit the list of sheet titles.
