using Autodesk.Revit.Attributes;
using Autodesk.Revit.DB;
using Autodesk.Revit.DB.Architecture;
using Autodesk.Revit.UI;
using FreeAxez.Addin.AdditionalFunctional.LowVoltagePath.Constants;
using FreeAxez.Addin.AdditionalFunctional.LowVoltagePath.Services;
using FreeAxez.Addin.Infrastructure;
using FreeAxez.Addin.Infrastructure.UI.Services;
using FreeAxez.Addin.Infrastructure.UI.Enums;

namespace FreeAxez.Addin.AdditionalFunctional.LowVoltagePathAnnotationUpdate
{
    /// <summary>
    /// Command to update low voltage annotation wire counts based on connected outlets and railings
    /// </summary>
    [Transaction(TransactionMode.Manual)]
    [Regeneration(RegenerationOption.Manual)]
    internal class LowVoltagePathAnnotationUpdateCommand : BaseExternalCommand
    {
        public override Result Execute()
        {
            try
            {
                // Validate that current view is a plan
                if (!(RevitManager.Document.ActiveView is ViewPlan))
                {
                    MessageWindow.ShowDialog("Warning", "This command only works with floor plan views.", MessageType.Notify);
                    return Result.Cancelled;
                }

                // Collect data from active view
                var dataCollector = new LowVoltageDataCollector();
                var outlets = dataCollector.CollectLowVoltageOutlets();
                var railings = CollectLowVoltageRailings();
                var annotations = CollectLowVoltageAnnotations();

                // Validate collected data
                if (railings.Count == 0 && annotations.Count == 0)
                {
                    MessageWindow.ShowDialog("Information", "No low voltage railings or annotations found in the active view.", MessageType.Notify);
                    return Result.Succeeded;
                }

                // Update wire counts
                if (railings.Count > 0 || annotations.Count > 0)
                {
                    var wireCountingService = new WireCountingService(railings, annotations, outlets);
                    wireCountingService.CountWires();

                    var message = $"Successfully updated wire counts:\n" +
                                  $"- Railings: {railings.Count}\n" +
                                  $"- Annotations: {annotations.Count}\n" +
                                  $"- Outlets processed: {outlets.Count}";

                    MessageWindow.ShowDialog("Success", message, MessageType.Success);
                }

                return Result.Succeeded;
            }
            catch (Exception ex)
            {
                MessageWindow.ShowDialog("Error", $"An error occurred while updating annotations: {ex.Message}", MessageType.Error);
                return Result.Failed;
            }
        }

        /// <summary>
        /// Collects all railings in the active view that have the Quantity parameter
        /// </summary>
        private List<Railing> CollectLowVoltageRailings()
        {
            return new FilteredElementCollector(RevitManager.Document, RevitManager.Document.ActiveView.Id)
                .OfClass(typeof(Railing))
                .WhereElementIsNotElementType()
                .Cast<Railing>()
                .ToList();
        }

        /// <summary>
        /// Collects all low voltage pathway annotations in the active view
        /// </summary>
        private List<FamilyInstance> CollectLowVoltageAnnotations()
        {
            return new FilteredElementCollector(RevitManager.Document, RevitManager.Document.ActiveView.Id)
                .OfCategory(BuiltInCategory.OST_GenericAnnotation)
                .WhereElementIsNotElementType()
                .Cast<FamilyInstance>()
                .Where(annotation => annotation.Symbol.FamilyName.Contains(LowVoltageConstants.AnnotationFamilyName))
                .ToList();
        }
    }
}
