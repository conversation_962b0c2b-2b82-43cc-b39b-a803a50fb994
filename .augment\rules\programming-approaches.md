---
type: "agent_requested"
description: "Example description"
---
### Architectural Patterns
- **UI Pattern**: WPF with MVVM (Model-View-ViewModel)
- **Data Access**: Repository pattern with Unit of Work for medium projects
- **Dependency Injection**: Microsoft.Extensions.DependencyInjection
- **Configuration**: Microsoft.Extensions.Configuration with appsettings.json
- **Logging**: Custom LogHelper class

### Design Principles
- **SOLID principles**: Mandatory adherence to all five principles
- **DRY (Don't Repeat Yourself)**: Avoid code duplication
- **YAGNI (You Aren't Gonna Need It)**: Don't implement "future" functionality
- **KISS (Keep It Simple, Stupid)**: Choose the simplest working approach
- **Composition over Inheritance**: Prefer composition over inheritance

### Error Handling
- **Exception handling**: Use specific exception types
- **Try-catch blocks**: Only catch exceptions that can be handled
- **Async/await**: Always use ConfigureAwait(false) in library code
- **Result pattern**: For methods that can return error or result

### Data Operations
- **Entity Framework Core**: For ORM (code-first approach)
- **AutoMapper**: For mapping between DTO and Entity
- **FluentValidation**: For model validation
- **Pagination**: Use IQueryable for large datasets

### Testing
- **Unit Tests**: xUnit framework
- **Test Naming**: Should_ReturnExpectedResult_When_ConditionMet
- **Arrange-Act-Assert**: Unit test structure
- **Mocking**: Moq framework for creating mock objects
- **Coverage**: Minimum 80% code coverage for critical business logic

### Async Programming
- **Async/Await**: Use for I/O operations
- **Task.Run**: Avoid for CPU-bound operations in ASP.NET
- **CancellationToken**: Pass to async methods for cancellation capability
- **ConfigureAwait**: Use ConfigureAwait(false) in library code

### Performance and Memory
- **StringBuilder**: For concatenating large numbers of strings
- **Span<T> and Memory<T>**: For high-performance code
- **Object pooling**: For frequently created objects
- **IDisposable**: Implement for resources requiring cleanup
- **Using statements**: For automatic resource disposal

### Security
- **Input validation**: Validate all input data
- **SQL Injection**: Use parameterized queries
- **Authentication**: ASP.NET Core Identity for web applications
- **Secrets management**: Azure Key Vault or User Secrets for development
- **HTTPS**: Mandatory for production

### Project Structure
```
Solution/
├── src/
│   ├── Core/
│   │   ├── Domain/          # Entities, Value Objects
│   │   └── Application/     # Use Cases, Services, DTOs
│   ├── Infrastructure/      # Data Access, External Services
│   ├── Presentation/        # UI Layer (WPF, Web API)
│   └── Shared/             # Cross-cutting concerns
└── tests/
    ├── UnitTests/
    └── IntegrationTests/
```

### Code Quality Tools
- **EditorConfig**: For consistent formatting
- **StyleCop**: For code style checking
- **SonarAnalyzer**: For static code analysis
- **Nullable Reference Types**: Enable in project (.NET 6+)

### Git and Version Control
- **Commit messages**: Conventional Commits format
- **Branch naming**: feature/task-description, bugfix/issue-description
- **Pull Requests**: Mandatory code review before merge
- **Gitignore**: Use standard .NET gitignore template