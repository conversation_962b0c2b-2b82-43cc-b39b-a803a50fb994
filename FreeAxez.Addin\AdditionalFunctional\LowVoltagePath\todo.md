# План рефакторингу LowVoltagePath

## Головна мета
Переробити архітектуру інструменту для чіткого розділення UI та бізнес-логіки, спростити структуру сервісів, покращити читабельність коду та забезпечити надійну роботу з Revit API.

---

### Крок 1: Розділення UI та бізнес-логіки

-  Рефакторинг `LowVoltagePathViewModel.cs`:
  -  Спростити ViewModel, щоб він відповідав лише за збір налаштувань користувача.
  -  Вся логіка по збору даних має зберігати результат в модель `LowVoltagePathSettings`.
-  Оновити логіку команди "Виконати":
  -  Команда повинна створювати екземпляр `LowVoltageProcessor`.
  -  Передавати об'єкт `LowVoltagePathSettings` в процесор.
  -  Закривати вікно UI одразу після передачі керування.

### Крок 2: Консолідація та реорганізація сервісів

-  Створити `DataAnalysisService`:
  -  Перенести та об'єднати логіку з `LowVoltageDataCollector` та `LowVoltageValidationService`.
-  Створити фасад `PathGeometryService` для всіх геометричних обчислень:
  -  Перейменувати `AnnotationPlacerService` в `AnnotationPositionCalculator` і зробити його внутрішнім компонентом `PathGeometryService`.
  -  Перенести логіку побудови шляхів з `GeometryAnalyzerService` у внутрішній компонент.
  -  Перенести логіку з `LineNormalizationService` та `LineGroupService` у внутрішні компоненти.
-  Створити `RevitCreationService`:
  -  Перенести логіку створення огороджень з `RailingsGeneratorService`.
  -  Перенести логіку безпосереднього створення анотацій (використовуючи результати від `AnnotationPositionCalculator`).
-  Перемістити `NTSConverter` в папку `Utils`, оскільки це технічна утиліта.
-  Переконатись, що `WireCountingService` та `LowVoltageReportService` залишаються незалежними.
-  Оновити `LowVoltageProcessor`, щоб він коректно викликав нові сервіси в правильній послідовності.
-  Видалити старі файли сервісів, які були об'єднані:
  -  `LowVoltageDataCollector.cs`
  -  `LowVoltageValidationService.cs`
  -  `GeometryAnalyzerService.cs`
  -  `LineNormalizationService.cs`
  -  `LineGroupService.cs`
  -  `AnnotationPlacerService.cs`
  -  `RailingsGeneratorService.cs`

### Крок 3: Покращення якості коду

-  Централізувати константи:
  -  Переглянути весь код і перенести "магічні" рядки та числа (назви параметрів, шляхів, ключів) у `Constants/LowVoltageConstants.cs`.
  -  Замінити всі входження в коді на посилання на константи.
-  Додати XML-коментарі (`/// <summary>...</summary>`) до всіх **публічних** методів у нових сервісах, моделях та ViewModel.
-  Провести рефакторинг довгих та складних методів, розбивши їх на менші логічні частини.
-  Перевірити та покращити імена змінних, методів та класів відповідно до їх нової ролі.

### Крок 4: Оптимізація роботи з Revit API

-  Перевірити, що всі операції, які змінюють модель Revit, виконуються в межах однієї транзакції в `RevitCreationService`.
-  Огорнути всі виклики до Revit API в блоки `try-catch` для надійної обробки помилок.
-  Переконатись, що всі `IDisposable` об'єкти Revit звільняються за допомогою блоків `using`.

### Крок 5: Фінальний огляд

-  Переглянути використання моделей `ExecutionReport` та `ValidationResult`, щоб переконатись, що вони ефективно використовуються для передачі даних між сервісами без прив'язки до UI.
-  Видалити всі невикористовувані файли та залежності (`using`).
-  Провести фінальне тестування функціоналу.
