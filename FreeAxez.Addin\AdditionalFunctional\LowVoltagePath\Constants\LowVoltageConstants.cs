namespace FreeAxez.Addin.AdditionalFunctional.LowVoltagePath.Constants
{
    /// <summary>
    /// Constants for Low Voltage Path functionality
    /// </summary>
    public static class LowVoltageConstants
    {
        #region Parameter Names

        /// <summary>
        /// Parameter name for wire quantity in railings and annotations
        /// </summary>
        public const string QuantityParameterName = "Quantity";

        #endregion

        #region Family Names

        /// <summary>
        /// Family name for low voltage pathway arrow annotations
        /// </summary>
        public const string AnnotationFamilyName = "FA-Low_Voltage_Pathway_Arrow_Annotation";

        #endregion

        #region Regex Patterns

        /// <summary>
        /// Regex pattern for matching low voltage line styles (LV1, LV_4, MC - 2, etc.)
        /// </summary>
        public const string LowVoltageLinePattern = @"(LV|MC)[\s-_]*\d";

        /// <summary>
        /// Regex pattern for matching low voltage count parameters (LV1 - Count, MC 2 - Count, etc.)
        /// </summary>
        public const string LowVoltageCountParameterPattern = @"^(LV|MC)[\s-_]*\d[\s-_]*Count$";

        #endregion

        #region Tolerances and Distances

        /// <summary>
        /// Tolerance for annotation placement near railings (1 inch in feet)
        /// </summary>
        public const double AnnotationToleranceFeet = 0.0833334;

        /// <summary>
        /// Distance tolerance for outlet-to-line association (2 feet)
        /// </summary>
        public const double OutletToLineToleranceFeet = 2.0;

        #endregion
    }
}
