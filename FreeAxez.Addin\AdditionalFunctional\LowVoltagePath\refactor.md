# Ключові моменти для малого проекту:
- Архітектурна простота - один основний ViewModel, мінімум абстракцій, прямолінійна логіка
- MVVM без фанатизму - базовий біндинг, команди, але без складних паттернів
- Revit API оптимізація - правильні транзакції, кешування, обробка винятків
- Читабельність коду - змістовні назви, короткі методи, мінімум вкладеності

# Чого уникати (оверінжиніринг для 2-5K рядків):
- DI контейнерів
- Складних паттернів (Command Bus, Event Aggregator)
- Генеричних репозиторіїв
- Onion Architecture

# Що обов'язково треба:
- SOLID принципи (адаптовано для малого проекту)
    - Single Responsibility: один клас = одна відповідальність
    - Open/Closed: використання інтерфейсів для розширення
    - Dependency Inversion: залежність від абстракцій (мінімально)
- Proper disposal ресурсів
- Try-catch для Revit API
- INotifyPropertyChanged для UI
- Валідація користувацького вводу
- Збереження налаштувань

Такий підхід забезпечить стабільний, підтримуваний код без зайвої складності.