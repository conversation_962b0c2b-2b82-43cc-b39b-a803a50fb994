﻿using Autodesk.Revit.DB;
using Autodesk.Revit.DB.Architecture;
using FreeAxez.Addin.AdditionalFunctional.LowVoltagePath.Models;
using FreeAxez.Addin.Infrastructure;
using System.Text.RegularExpressions;

namespace FreeAxez.Addin.AdditionalFunctional.LowVoltagePath.Services
{
    public class AnnotationTypeSelector
    {
        private readonly RailingType _selectedRailingType;
        private readonly List<FamilySymbol> _annotationTypes = new List<FamilySymbol>();

        public AnnotationTypeSelector(RailingType selectedRailingType)
        {
            _selectedRailingType = selectedRailingType;

            _annotationTypes = new FilteredElementCollector(RevitManager.Document)
                .OfCategory(BuiltInCategory.OST_GenericAnnotation)
                .WhereElementIsElementType()
                .Cast<FamilySymbol>()
                .Where(fs => fs.FamilyName == Constants.LowVoltageConstants.AnnotationFamilyName)
                .ToList();
        }

        /// <summary>
        /// Gets annotation symbol for the specified direction
        /// </summary>
        public FamilySymbol GetAnnotationType(Direction direction)
        {
            var filteredTypes = _annotationTypes
                .Where(s => s.Name.ToUpper().Contains(GetOppositeDirection(direction).ToUpper()))
                .ToList();

            if (filteredTypes.Count == 0) return null;
            else if (filteredTypes.Count == 1) return filteredTypes.First();

            var railMark = ExtractMark(_selectedRailingType.Name);

            foreach (var type in filteredTypes)
            {
                var annotationMark = ExtractMark(type.Name);

                if (railMark != null 
                    && annotationMark != null 
                    && railMark == annotationMark)
                {
                    return type;
                }
            }

            return filteredTypes.FirstOrDefault();
        }

        private string GetOppositeDirection(Direction direction)
        {
            return direction switch
            {
                Direction.Left => Direction.Right.ToString(),
                Direction.Right => Direction.Left.ToString(),
                Direction.Up => Direction.Down.ToString(),
                Direction.Down => Direction.Up.ToString(),
                _ => null
            };
        }

        private string ExtractMark(string name)
        {
            var match = Regex.Match(name, Constants.LowVoltageConstants.LowVoltageLinePattern);
            if (!match.Success) return null;
            return Regex.Replace(match.Value, @"[\s-_]", "");
        }
    }
}
